@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-rgb: 24, 119, 242;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-rgb: 56, 139, 253;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .neo-card {
    @apply bg-black/20 backdrop-blur-lg border border-white/10 rounded-xl shadow-[0_0_15px_rgba(56,139,253,0.15)];
  }

  .neo-glow {
    @apply shadow-[0_0_15px_rgba(56,139,253,0.3)];
  }

  .neo-text {
    @apply text-white drop-shadow-[0_0_8px_rgba(56,139,253,0.8)];
  }

  .floating {
    animation: float 6s ease-in-out infinite;
  }

  .floating-delay-1 {
    animation: float 6s ease-in-out 1s infinite;
  }

  .floating-delay-2 {
    animation: float 6s ease-in-out 2s infinite;
  }

  .floating-delay-3 {
    animation: float 6s ease-in-out 3s infinite;
  }
  
  .animate-slow-spin {
    animation: slow-spin 20s linear infinite;
  }

  .animate-float-3d {
    animation: float-3d 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:scale-110:hover {
    transform: scale(1.1);
  }

  .hover\:-translate-y-1:hover {
    transform: translateY(-0.25rem);
  }

  .hover\:rotate-3:hover {
    transform: rotate(3deg);
  }

  .hover\:-rotate-3:hover {
    transform: rotate(-3deg);
  }

  .hover\:-rotate-12:hover {
    transform: rotate(-12deg);
  }

  .hover\:rotate-12:hover {
    transform: rotate(12deg);
  }

  .hover\:rotate-45:hover {
    transform: rotate(45deg);
  }

  .hover\:-rotate-45:hover {
    transform: rotate(-45deg);
  }

  /* Galaxy-specific styles */
  .galaxy-glow {
    @apply shadow-[0_0_30px_rgba(79,70,229,0.4),0_0_60px_rgba(124,58,237,0.2)];
  }

  .cosmic-text {
    @apply text-white drop-shadow-[0_0_12px_rgba(255,255,255,0.8)];
  }

  .nebula-card {
    @apply bg-black/30 backdrop-blur-xl border border-white/20 rounded-xl shadow-[0_0_25px_rgba(79,70,229,0.2)];
  }

  .star-twinkle {
    animation: star-twinkle 3s ease-in-out infinite;
  }

  .cosmic-drift {
    animation: cosmic-drift 20s linear infinite;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes slow-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float-3d {
  0% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
  33% {
    transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  }
  66% {
    transform: translateY(-5px) rotateX(-5deg) rotateY(-5deg);
  }
  100% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(56, 139, 253, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(56, 139, 253, 0.6);
  }
}

@keyframes star-twinkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes cosmic-drift {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
  25% {
    transform: translateX(10px) translateY(-5px) rotate(90deg);
  }
  50% {
    transform: translateX(0) translateY(-10px) rotate(180deg);
  }
  75% {
    transform: translateX(-10px) translateY(-5px) rotate(270deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(360deg);
  }
}

.font-sans {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
}

