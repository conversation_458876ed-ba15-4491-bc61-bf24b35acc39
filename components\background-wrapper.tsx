"use client"

import { Interactive3DBackground } from "@/components/interactive-3d-background"
import { ParticleBackground } from "@/components/particle-background"
import { useActiveSection } from "@/hooks/use-active-section"

export function BackgroundWrapper() {
  const activeSection = useActiveSection()

  return (
    <div className="fixed inset-0 -z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.15),transparent_70%)]" />
      <ParticleBackground />
      <Interactive3DBackground section={activeSection} />
    </div>
  )
}
