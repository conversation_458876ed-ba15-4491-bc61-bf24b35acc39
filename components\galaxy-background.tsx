"use client"

import { useEffect, useRef, useState } from "react"
import NebulaEffects from "./nebula-effects"
import AuroraEffects from "./aurora-effects"

interface Star {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  twinkleSpeed: number
  twinkleOffset: number
}

interface ShootingStar {
  x: number
  y: number
  vx: number
  vy: number
  length: number
  opacity: number
  color: string
  life: number
  maxLife: number
}

interface GalaxyParticle {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  angle: number
  distance: number
  speed: number
}

interface SunFlare {
  x: number
  y: number
  size: number
  opacity: number
  color: string
  pulseSpeed: number
  pulseOffset: number
}

interface Interactive3DBackgroundProps {
  section: string
}

export function GalaxyBackground({ section }: Interactive3DBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let stars: Star[] = []
    let galaxyParticles: GalaxyParticle[] = []
    let shootingStars: ShootingStar[] = []
    let sunFlares: SunFlare[] = []
    let time = 0
    let lastShootingStarTime = 0

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      initGalaxy()
    }

    // Mouse tracking for minimal parallax effect
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = {
        x: (e.clientX / window.innerWidth) * 0.3 - 0.15, // Much smaller range
        y: (e.clientY / window.innerHeight) * 0.3 - 0.15
      }
    }

    // Initialize galaxy
    const initGalaxy = () => {
      stars = []
      galaxyParticles = []
      sunFlares = []

      // Create realistic starfield
      const starCount = Math.floor((window.innerWidth * window.innerHeight) / 2000)
      for (let i = 0; i < starCount; i++) {
        stars.push(createStar())
      }

      // Create galaxy spiral particles (much fewer and more subtle)
      const galaxyCount = Math.floor((window.innerWidth * window.innerHeight) / 40000)
      for (let i = 0; i < galaxyCount; i++) {
        galaxyParticles.push(createGalaxyParticle())
      }

      // Create sun flares on the left side
      const flareCount = 3
      for (let i = 0; i < flareCount; i++) {
        sunFlares.push(createSunFlare())
      }

      setIsLoaded(true)
    }

    // Create a realistic star
    const createStar = (): Star => {
      const starTypes = [
        { color: '#ffffff', size: 0.8, opacity: 0.9 }, // Small white stars
        { color: '#ffffff', size: 1.2, opacity: 0.8 }, // Medium white stars
        { color: '#ffffcc', size: 1.0, opacity: 0.85 }, // Warm white stars
        { color: '#ccddff', size: 1.4, opacity: 0.7 }, // Blue-white stars
        { color: '#ffddcc', size: 0.9, opacity: 0.6 }, // Red-white stars
        { color: '#ffffff', size: 2.0, opacity: 1.0 }, // Bright stars (rare)
      ]

      // More variety in star types
      const type = Math.random() < 0.6 ? starTypes[0] : starTypes[Math.floor(Math.random() * starTypes.length)]

      return {
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        z: Math.random() * 800 + 200, // Keep stars closer for better visibility
        size: type.size + Math.random() * 0.4,
        color: type.color,
        opacity: type.opacity * (0.6 + Math.random() * 0.4),
        twinkleSpeed: Math.random() * 0.005 + 0.001, // Slower twinkling
        twinkleOffset: Math.random() * Math.PI * 2
      }
    }

    // Create galaxy dust particle (very subtle, no circles)
    const createGalaxyParticle = (): GalaxyParticle => {
      const centerX = canvas.width * 0.2 // Far left side
      const centerY = canvas.height * 0.3
      const angle = Math.random() * Math.PI * 6 // Multiple spiral arms
      const distance = Math.random() * Math.min(canvas.width, canvas.height) * 0.6

      return {
        x: centerX + Math.cos(angle) * distance,
        y: centerY + Math.sin(angle) * distance,
        z: Math.random() * 800,
        size: Math.random() * 120 + 80, // Larger, more diffuse
        color: '#0a0a1a',
        opacity: Math.random() * 0.03 + 0.01, // Much more subtle
        angle,
        distance,
        speed: Math.random() * 0.0003 + 0.0001 // Much slower
      }
    }

    // Create sun flare effect
    const createSunFlare = (): SunFlare => {
      return {
        x: Math.random() * canvas.width * 0.4, // Left side
        y: Math.random() * canvas.height,
        size: Math.random() * 200 + 100,
        opacity: Math.random() * 0.15 + 0.05,
        color: '#ffd700',
        pulseSpeed: Math.random() * 0.01 + 0.005,
        pulseOffset: Math.random() * Math.PI * 2
      }
    }

    // Create shooting star
    const createShootingStar = (): ShootingStar => {
      const side = Math.floor(Math.random() * 4)
      let x, y, vx, vy

      switch (side) {
        case 0: // From top
          x = Math.random() * canvas.width
          y = -50
          vx = (Math.random() - 0.5) * 3
          vy = Math.random() * 2 + 1
          break
        case 1: // From right
          x = canvas.width + 50
          y = Math.random() * canvas.height
          vx = -(Math.random() * 2 + 1)
          vy = (Math.random() - 0.5) * 3
          break
        case 2: // From bottom
          x = Math.random() * canvas.width
          y = canvas.height + 50
          vx = (Math.random() - 0.5) * 3
          vy = -(Math.random() * 2 + 1)
          break
        default: // From left
          x = -50
          y = Math.random() * canvas.height
          vx = Math.random() * 2 + 1
          vy = (Math.random() - 0.5) * 3
      }

      const maxLife = Math.random() * 80 + 40

      return {
        x, y, vx, vy,
        length: Math.random() * 60 + 30,
        opacity: 1,
        color: '#ffffff',
        life: maxLife,
        maxLife
      }
    }

    // Draw star with cross pattern (no circles)
    const drawStar = (star: Star) => {
      const scale = 800 / (800 + star.z)
      const x = star.x + mouseRef.current.x * 2 * scale // Much less movement
      const y = star.y + mouseRef.current.y * 2 * scale
      const size = star.size * scale

      // Twinkling effect
      const twinkle = Math.sin(time * star.twinkleSpeed + star.twinkleOffset) * 0.3 + 0.7
      const opacity = star.opacity * twinkle * scale

      ctx.save()
      ctx.globalAlpha = opacity
      ctx.fillStyle = star.color

      // Draw star as a cross/plus shape for more realistic look
      const armLength = size * 1.5
      const armWidth = size * 0.2

      // Horizontal arm
      ctx.fillRect(x - armLength, y - armWidth, armLength * 2, armWidth * 2)
      // Vertical arm
      ctx.fillRect(x - armWidth, y - armLength, armWidth * 2, armLength * 2)

      // Add bright center point (small square, not circle)
      const centerSize = size * 0.6
      ctx.fillRect(x - centerSize/2, y - centerSize/2, centerSize, centerSize)

      ctx.restore()
    }

    // Draw galaxy particle as irregular dust cloud (no circles)
    const drawGalaxyParticle = (particle: GalaxyParticle) => {
      const scale = 800 / (800 + particle.z)
      const x = particle.x + mouseRef.current.x * 5 * scale // Much less movement
      const y = particle.y + mouseRef.current.y * 5 * scale
      const size = particle.size * scale

      ctx.save()
      ctx.globalAlpha = particle.opacity * scale * 0.2 // Much more subtle

      // Create very diffuse gradient
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size)
      gradient.addColorStop(0, particle.color + '20')
      gradient.addColorStop(0.4, particle.color + '10')
      gradient.addColorStop(0.8, particle.color + '05')
      gradient.addColorStop(1, 'transparent')

      ctx.fillStyle = gradient

      // Create irregular dust cloud shape instead of circle
      ctx.beginPath()
      const points = 12
      for (let i = 0; i < points; i++) {
        const angle = (i / points) * Math.PI * 2
        const radius = size * (0.5 + Math.random() * 0.8) // Irregular radius
        const px = x + Math.cos(angle) * radius
        const py = y + Math.sin(angle) * radius
        if (i === 0) ctx.moveTo(px, py)
        else ctx.lineTo(px, py)
      }
      ctx.closePath()
      ctx.fill()

      ctx.restore()
    }

    // Draw sun flare as organic light burst (no circles)
    const drawSunFlare = (flare: SunFlare) => {
      const pulse = Math.sin(time * flare.pulseSpeed + flare.pulseOffset) * 0.2 + 0.8
      const opacity = flare.opacity * pulse

      ctx.save()
      ctx.globalAlpha = opacity

      // Create sun flare gradient
      const gradient = ctx.createRadialGradient(flare.x, flare.y, 0, flare.x, flare.y, flare.size)
      gradient.addColorStop(0, flare.color + '40')
      gradient.addColorStop(0.3, flare.color + '25')
      gradient.addColorStop(0.6, flare.color + '15')
      gradient.addColorStop(1, 'transparent')

      ctx.fillStyle = gradient

      // Create organic sun flare shape with rays
      ctx.beginPath()
      const rays = 16
      for (let i = 0; i < rays; i++) {
        const angle = (i / rays) * Math.PI * 2
        const innerRadius = flare.size * 0.3
        const outerRadius = flare.size * (0.8 + Math.sin(time * 2 + angle * 3) * 0.3)

        const x1 = flare.x + Math.cos(angle) * innerRadius
        const y1 = flare.y + Math.sin(angle) * innerRadius
        const x2 = flare.x + Math.cos(angle) * outerRadius
        const y2 = flare.y + Math.sin(angle) * outerRadius

        if (i === 0) ctx.moveTo(x1, y1)
        ctx.lineTo(x2, y2)
        ctx.lineTo(flare.x + Math.cos(angle + Math.PI / rays) * innerRadius,
                   flare.y + Math.sin(angle + Math.PI / rays) * innerRadius)
      }
      ctx.closePath()
      ctx.fill()

      ctx.restore()
    }

    // Draw shooting star
    const drawShootingStar = (star: ShootingStar) => {
      const opacity = star.opacity * (star.life / star.maxLife)

      ctx.save()
      ctx.globalAlpha = opacity
      ctx.strokeStyle = star.color
      ctx.lineWidth = 1.5
      ctx.lineCap = 'round'

      // Create gradient tail
      const gradient = ctx.createLinearGradient(
        star.x, star.y,
        star.x - star.vx * star.length / 4, star.y - star.vy * star.length / 4
      )
      gradient.addColorStop(0, star.color)
      gradient.addColorStop(1, 'transparent')

      ctx.strokeStyle = gradient
      ctx.beginPath()
      ctx.moveTo(star.x, star.y)
      ctx.lineTo(
        star.x - star.vx * star.length / 4,
        star.y - star.vy * star.length / 4
      )
      ctx.stroke()

      // Bright head (diamond shape instead of circle)
      ctx.fillStyle = star.color
      ctx.beginPath()
      ctx.moveTo(star.x, star.y - 2)
      ctx.lineTo(star.x + 1.5, star.y)
      ctx.lineTo(star.x, star.y + 2)
      ctx.lineTo(star.x - 1.5, star.y)
      ctx.closePath()
      ctx.fill()

      ctx.restore()
    }

    // Animation loop
    const animate = (currentTime: number) => {
      time = currentTime * 0.001

      // Create shooting stars occasionally
      if (currentTime - lastShootingStarTime > 4000 + Math.random() * 6000) {
        shootingStars.push(createShootingStar())
        lastShootingStarTime = currentTime
      }

      // Clear canvas with deep space background
      ctx.fillStyle = '#000008'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Add subtle gradient overlay for depth
      const gradient = ctx.createRadialGradient(
        canvas.width * 0.3, canvas.height * 0.4, 0,
        canvas.width * 0.3, canvas.height * 0.4, Math.max(canvas.width, canvas.height) * 0.8
      )
      gradient.addColorStop(0, 'rgba(10, 10, 25, 0.2)')
      gradient.addColorStop(0.5, 'rgba(5, 5, 15, 0.4)')
      gradient.addColorStop(1, 'rgba(0, 0, 8, 0.8)')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw sun flares first (background layer)
      sunFlares.forEach(flare => {
        drawSunFlare(flare)
      })

      // Update and draw galaxy particles
      galaxyParticles.forEach(particle => {
        particle.angle += particle.speed
        particle.x = (canvas.width * 0.2) + Math.cos(particle.angle) * particle.distance
        particle.y = (canvas.height * 0.3) + Math.sin(particle.angle) * particle.distance * 0.5

        drawGalaxyParticle(particle)
      })

      // Update and draw shooting stars
      shootingStars = shootingStars.filter(star => {
        star.x += star.vx
        star.y += star.vy
        star.life--
        star.opacity = star.life / star.maxLife

        if (star.life <= 0 ||
            star.x < -100 || star.x > canvas.width + 100 ||
            star.y < -100 || star.y > canvas.height + 100) {
          return false
        }

        drawShootingStar(star)
        return true
      })

      // Draw stars (foreground layer) - keep them mostly stationary
      stars.forEach(star => {
        drawStar(star)
      })

      animationFrameId = requestAnimationFrame(animate)
    }

    // Initialize
    resizeCanvas()
    animate(0)

    // Event listeners
    window.addEventListener("resize", resizeCanvas)
    window.addEventListener("mousemove", handleMouseMove)

    // Cleanup
    return () => {
      window.removeEventListener("resize", resizeCanvas)
      window.removeEventListener("mousemove", handleMouseMove)
      cancelAnimationFrame(animationFrameId)
    }
  }, [section])

  return (
    <div className="absolute inset-0 z-0">
      {/* Nebula background layer */}
      <NebulaEffects section={section} />

      {/* Aurora effects layer */}
      <AuroraEffects section={section} />

      {/* Main galaxy canvas */}
      <canvas
        ref={canvasRef}
        className={`absolute inset-0 z-10 pointer-events-none transition-opacity duration-1000 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        aria-hidden="true"
      />
    </div>
  )
}
