"use client"

import { useEffect, useRef, useState } from "react"

interface Star {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  twinkleSpeed: number
  twinkleOffset: number
}

interface NebulaParticle {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  vx: number
  vy: number
  vz: number
}

interface ShootingStar {
  x: number
  y: number
  vx: number
  vy: number
  length: number
  opacity: number
  color: string
  life: number
  maxLife: number
}

interface Interactive3DBackgroundProps {
  section: string
}

export function GalaxyBackground({ section }: Interactive3DBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let stars: Star[] = []
    let nebulaParticles: NebulaParticle[] = []
    let shootingStars: ShootingStar[] = []
    let time = 0
    let lastShootingStarTime = 0

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      initGalaxy()
    }

    // Mouse tracking for parallax effect
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = {
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: (e.clientY / window.innerHeight) * 2 - 1
      }
    }

    // Initialize galaxy based on current section
    const initGalaxy = () => {
      stars = []
      nebulaParticles = []
      
      // Create stars
      const starCount = Math.floor((window.innerWidth * window.innerHeight) / 8000)
      for (let i = 0; i < starCount; i++) {
        stars.push(createStar())
      }

      // Create nebula particles based on section
      const nebulaCount = Math.floor((window.innerWidth * window.innerHeight) / 20000)
      for (let i = 0; i < nebulaCount; i++) {
        nebulaParticles.push(createNebulaParticle(section))
      }

      setIsLoaded(true)
    }

    // Create a star
    const createStar = (): Star => {
      const starTypes = [
        { color: '#ffffff', size: 0.5, opacity: 0.8 }, // White dwarf
        { color: '#ffd700', size: 1, opacity: 0.9 },   // Yellow star
        { color: '#87ceeb', size: 0.8, opacity: 0.7 }, // Blue star
        { color: '#ffb6c1', size: 0.6, opacity: 0.6 }, // Red star
        { color: '#e6e6fa', size: 1.2, opacity: 1 },   // Bright star
      ]
      
      const type = starTypes[Math.floor(Math.random() * starTypes.length)]
      
      return {
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        z: Math.random() * 1000,
        size: type.size + Math.random() * 0.5,
        color: type.color,
        opacity: type.opacity,
        twinkleSpeed: Math.random() * 0.02 + 0.01,
        twinkleOffset: Math.random() * Math.PI * 2
      }
    }

    // Create shooting star
    const createShootingStar = (): ShootingStar => {
      const side = Math.floor(Math.random() * 4) // 0: top, 1: right, 2: bottom, 3: left
      let x, y, vx, vy

      switch (side) {
        case 0: // From top
          x = Math.random() * canvas.width
          y = -50
          vx = (Math.random() - 0.5) * 4
          vy = Math.random() * 3 + 2
          break
        case 1: // From right
          x = canvas.width + 50
          y = Math.random() * canvas.height
          vx = -(Math.random() * 3 + 2)
          vy = (Math.random() - 0.5) * 4
          break
        case 2: // From bottom
          x = Math.random() * canvas.width
          y = canvas.height + 50
          vx = (Math.random() - 0.5) * 4
          vy = -(Math.random() * 3 + 2)
          break
        default: // From left
          x = -50
          y = Math.random() * canvas.height
          vx = Math.random() * 3 + 2
          vy = (Math.random() - 0.5) * 4
      }

      const maxLife = Math.random() * 60 + 30 // 30-90 frames

      return {
        x,
        y,
        vx,
        vy,
        length: Math.random() * 80 + 40,
        opacity: 1,
        color: '#ffffff',
        life: maxLife,
        maxLife
      }
    }

    // Create nebula particle based on section theme
    const createNebulaParticle = (currentSection: string): NebulaParticle => {
      const sectionThemes = {
        home: ['#4f46e5', '#7c3aed', '#2563eb'], // Purple/Blue
        about: ['#06b6d4', '#0891b2', '#0e7490'], // Cyan
        skills: ['#10b981', '#059669', '#047857'], // Green
        projects: ['#f59e0b', '#d97706', '#b45309'], // Orange
        experience: ['#8b5cf6', '#7c3aed', '#6d28d9'], // Purple
        contact: ['#ef4444', '#dc2626', '#b91c1c'] // Red
      }

      const colors = sectionThemes[currentSection as keyof typeof sectionThemes] || sectionThemes.home
      const color = colors[Math.floor(Math.random() * colors.length)]

      return {
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        z: Math.random() * 500,
        size: Math.random() * 30 + 10,
        color,
        opacity: Math.random() * 0.3 + 0.1,
        vx: (Math.random() - 0.5) * 0.2,
        vy: (Math.random() - 0.5) * 0.2,
        vz: (Math.random() - 0.5) * 0.1
      }
    }

    // Draw a star with twinkling effect
    const drawStar = (star: Star, deltaTime: number) => {
      const scale = 1000 / (1000 + star.z)
      const x = star.x + mouseRef.current.x * 20 * scale
      const y = star.y + mouseRef.current.y * 20 * scale
      const size = star.size * scale

      // Twinkling effect
      const twinkle = Math.sin(time * star.twinkleSpeed + star.twinkleOffset) * 0.3 + 0.7
      const opacity = star.opacity * twinkle * scale

      ctx.save()
      ctx.globalAlpha = opacity
      ctx.fillStyle = star.color
      
      // Draw star with glow effect
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 3)
      gradient.addColorStop(0, star.color)
      gradient.addColorStop(0.5, star.color + '80')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, size * 3, 0, Math.PI * 2)
      ctx.fill()
      
      // Draw bright center
      ctx.fillStyle = star.color
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
      
      ctx.restore()
    }

    // Draw shooting star
    const drawShootingStar = (star: ShootingStar) => {
      const opacity = star.opacity * (star.life / star.maxLife)

      ctx.save()
      ctx.globalAlpha = opacity
      ctx.strokeStyle = star.color
      ctx.lineWidth = 2
      ctx.lineCap = 'round'

      // Create gradient for the tail
      const gradient = ctx.createLinearGradient(
        star.x, star.y,
        star.x - star.vx * star.length / 5, star.y - star.vy * star.length / 5
      )
      gradient.addColorStop(0, star.color)
      gradient.addColorStop(1, 'transparent')

      ctx.strokeStyle = gradient
      ctx.lineWidth = 3

      // Draw the tail
      ctx.beginPath()
      ctx.moveTo(star.x, star.y)
      ctx.lineTo(
        star.x - star.vx * star.length / 5,
        star.y - star.vy * star.length / 5
      )
      ctx.stroke()

      // Draw the bright head
      ctx.fillStyle = star.color
      ctx.beginPath()
      ctx.arc(star.x, star.y, 2, 0, Math.PI * 2)
      ctx.fill()

      // Add glow effect
      const glowGradient = ctx.createRadialGradient(star.x, star.y, 0, star.x, star.y, 8)
      glowGradient.addColorStop(0, star.color + '80')
      glowGradient.addColorStop(1, 'transparent')
      ctx.fillStyle = glowGradient
      ctx.beginPath()
      ctx.arc(star.x, star.y, 8, 0, Math.PI * 2)
      ctx.fill()

      ctx.restore()
    }

    // Draw nebula particle
    const drawNebulaParticle = (particle: NebulaParticle) => {
      const scale = 500 / (500 + particle.z)
      const x = particle.x + mouseRef.current.x * 30 * scale
      const y = particle.y + mouseRef.current.y * 30 * scale
      const size = particle.size * scale

      ctx.save()
      ctx.globalAlpha = particle.opacity * scale
      
      // Create nebula gradient
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size)
      gradient.addColorStop(0, particle.color + '60')
      gradient.addColorStop(0.5, particle.color + '30')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
      
      ctx.restore()
    }

    // Animation loop
    const animate = (currentTime: number) => {
      const deltaTime = currentTime / 16.67 // Normalize to ~60 FPS
      time = currentTime * 0.001 // Convert to seconds

      // Create shooting stars occasionally
      if (currentTime - lastShootingStarTime > 3000 + Math.random() * 5000) { // Every 3-8 seconds
        shootingStars.push(createShootingStar())
        lastShootingStarTime = currentTime
      }

      // Clear canvas with deep space background
      ctx.fillStyle = '#000011'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Add subtle gradient overlay
      const gradient = ctx.createRadialGradient(
        canvas.width / 2, canvas.height / 2, 0,
        canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height)
      )
      gradient.addColorStop(0, 'rgba(20, 20, 40, 0.3)')
      gradient.addColorStop(1, 'rgba(0, 0, 17, 0.8)')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Update and draw nebula particles
      nebulaParticles.forEach(particle => {
        particle.x += particle.vx
        particle.y += particle.vy
        particle.z += particle.vz

        // Wrap around edges
        if (particle.x < -50) particle.x = canvas.width + 50
        if (particle.x > canvas.width + 50) particle.x = -50
        if (particle.y < -50) particle.y = canvas.height + 50
        if (particle.y > canvas.height + 50) particle.y = -50
        if (particle.z < -250) particle.z = 250
        if (particle.z > 250) particle.z = -250

        drawNebulaParticle(particle)
      })

      // Update and draw shooting stars
      shootingStars = shootingStars.filter(star => {
        star.x += star.vx
        star.y += star.vy
        star.life--
        star.opacity = star.life / star.maxLife

        // Remove if off screen or life ended
        if (star.life <= 0 ||
            star.x < -100 || star.x > canvas.width + 100 ||
            star.y < -100 || star.y > canvas.height + 100) {
          return false
        }

        drawShootingStar(star)
        return true
      })

      // Update and draw stars
      stars.forEach(star => {
        // Subtle movement for parallax
        star.x += Math.sin(time * 0.1 + star.twinkleOffset) * 0.1
        star.y += Math.cos(time * 0.1 + star.twinkleOffset) * 0.1

        drawStar(star, deltaTime)
      })

      animationFrameId = requestAnimationFrame(animate)
    }

    // Initialize
    resizeCanvas()
    animate(0)

    // Event listeners
    window.addEventListener("resize", resizeCanvas)
    window.addEventListener("mousemove", handleMouseMove)

    // Cleanup
    return () => {
      window.removeEventListener("resize", resizeCanvas)
      window.removeEventListener("mousemove", handleMouseMove)
      cancelAnimationFrame(animationFrameId)
    }
  }, [section])

  return (
    <canvas 
      ref={canvasRef} 
      className={`absolute inset-0 z-0 pointer-events-none transition-opacity duration-1000 ${
        isLoaded ? 'opacity-100' : 'opacity-0'
      }`}
      aria-hidden="true" 
    />
  )
}
