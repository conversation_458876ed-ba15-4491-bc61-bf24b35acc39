"use client"

import { useEffect, useRef, useState } from "react"

interface Star {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  twinkleSpeed: number
  twinkleOffset: number
}

interface ShootingStar {
  x: number
  y: number
  vx: number
  vy: number
  length: number
  opacity: number
  color: string
  life: number
  maxLife: number
}

interface GalaxyParticle {
  x: number
  y: number
  z: number
  size: number
  color: string
  opacity: number
  angle: number
  distance: number
  speed: number
}

interface SunFlare {
  x: number
  y: number
  size: number
  opacity: number
  color: string
  pulseSpeed: number
  pulseOffset: number
}

interface Interactive3DBackgroundProps {
  section: string
}

export function GalaxyBackground({ section }: Interactive3DBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let stars: Star[] = []
    let galaxyParticles: GalaxyParticle[] = []
    let shootingStars: ShootingStar[] = []
    let sunFlares: SunFlare[] = []
    let time = 0
    let lastShootingStarTime = 0

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      initGalaxy()
    }

    // Mouse tracking for parallax effect
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = {
        x: (e.clientX / window.innerWidth) * 2 - 1,
        y: (e.clientY / window.innerHeight) * 2 - 1
      }
    }

    // Initialize galaxy
    const initGalaxy = () => {
      stars = []
      galaxyParticles = []
      sunFlares = []

      // Create realistic starfield
      const starCount = Math.floor((window.innerWidth * window.innerHeight) / 2000)
      for (let i = 0; i < starCount; i++) {
        stars.push(createStar())
      }

      // Create galaxy spiral particles
      const galaxyCount = Math.floor((window.innerWidth * window.innerHeight) / 15000)
      for (let i = 0; i < galaxyCount; i++) {
        galaxyParticles.push(createGalaxyParticle())
      }

      // Create sun flares on the left side
      const flareCount = 3
      for (let i = 0; i < flareCount; i++) {
        sunFlares.push(createSunFlare())
      }

      setIsLoaded(true)
    }

    // Create a realistic star
    const createStar = (): Star => {
      const starTypes = [
        { color: '#ffffff', size: 0.2, opacity: 0.8 }, // Tiny white stars
        { color: '#ffffff', size: 0.4, opacity: 0.6 }, // Small white stars
        { color: '#ffffcc', size: 0.3, opacity: 0.7 }, // Warm white stars
        { color: '#ccddff', size: 0.5, opacity: 0.5 }, // Blue-white stars
        { color: '#ffddcc', size: 0.3, opacity: 0.4 }, // Red-white stars
        { color: '#ffffff', size: 0.8, opacity: 1.0 }, // Bright stars (very rare)
      ]

      // 80% should be tiny white stars for realism
      const type = Math.random() < 0.8 ? starTypes[0] : starTypes[Math.floor(Math.random() * starTypes.length)]

      return {
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        z: Math.random() * 1000,
        size: type.size + Math.random() * 0.1,
        color: type.color,
        opacity: type.opacity * (0.3 + Math.random() * 0.7),
        twinkleSpeed: Math.random() * 0.008 + 0.002,
        twinkleOffset: Math.random() * Math.PI * 2
      }
    }

    // Create galaxy spiral particle
    const createGalaxyParticle = (): GalaxyParticle => {
      const centerX = canvas.width * 0.3 // Left side of screen
      const centerY = canvas.height * 0.4
      const angle = Math.random() * Math.PI * 4 // Multiple spiral arms
      const distance = Math.random() * Math.min(canvas.width, canvas.height) * 0.4

      return {
        x: centerX + Math.cos(angle) * distance,
        y: centerY + Math.sin(angle) * distance,
        z: Math.random() * 500,
        size: Math.random() * 60 + 20,
        color: '#1a1a3e',
        opacity: Math.random() * 0.1 + 0.02,
        angle,
        distance,
        speed: Math.random() * 0.001 + 0.0005
      }
    }

    // Create sun flare effect
    const createSunFlare = (): SunFlare => {
      return {
        x: Math.random() * canvas.width * 0.4, // Left side
        y: Math.random() * canvas.height,
        size: Math.random() * 200 + 100,
        opacity: Math.random() * 0.15 + 0.05,
        color: '#ffd700',
        pulseSpeed: Math.random() * 0.01 + 0.005,
        pulseOffset: Math.random() * Math.PI * 2
      }
    }

    // Create shooting star
    const createShootingStar = (): ShootingStar => {
      const side = Math.floor(Math.random() * 4)
      let x, y, vx, vy

      switch (side) {
        case 0: // From top
          x = Math.random() * canvas.width
          y = -50
          vx = (Math.random() - 0.5) * 3
          vy = Math.random() * 2 + 1
          break
        case 1: // From right
          x = canvas.width + 50
          y = Math.random() * canvas.height
          vx = -(Math.random() * 2 + 1)
          vy = (Math.random() - 0.5) * 3
          break
        case 2: // From bottom
          x = Math.random() * canvas.width
          y = canvas.height + 50
          vx = (Math.random() - 0.5) * 3
          vy = -(Math.random() * 2 + 1)
          break
        default: // From left
          x = -50
          y = Math.random() * canvas.height
          vx = Math.random() * 2 + 1
          vy = (Math.random() - 0.5) * 3
      }

      const maxLife = Math.random() * 80 + 40

      return {
        x, y, vx, vy,
        length: Math.random() * 60 + 30,
        opacity: 1,
        color: '#ffffff',
        life: maxLife,
        maxLife
      }
    }

    // Draw star with subtle glow
    const drawStar = (star: Star) => {
      const scale = 1000 / (1000 + star.z)
      const x = star.x + mouseRef.current.x * 15 * scale
      const y = star.y + mouseRef.current.y * 15 * scale
      const size = star.size * scale

      // Twinkling effect
      const twinkle = Math.sin(time * star.twinkleSpeed + star.twinkleOffset) * 0.2 + 0.8
      const opacity = star.opacity * twinkle * scale

      ctx.save()
      ctx.globalAlpha = opacity
      
      // Simple star point
      ctx.fillStyle = star.color
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
      
      ctx.restore()
    }

    // Draw galaxy particle with blur effect
    const drawGalaxyParticle = (particle: GalaxyParticle) => {
      const scale = 500 / (500 + particle.z)
      const x = particle.x + mouseRef.current.x * 25 * scale
      const y = particle.y + mouseRef.current.y * 25 * scale
      const size = particle.size * scale

      ctx.save()
      ctx.globalAlpha = particle.opacity * scale
      
      // Create gradient blur effect
      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size)
      gradient.addColorStop(0, particle.color + '40')
      gradient.addColorStop(0.3, particle.color + '20')
      gradient.addColorStop(0.7, particle.color + '10')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(x, y, size, 0, Math.PI * 2)
      ctx.fill()
      
      ctx.restore()
    }

    // Draw sun flare
    const drawSunFlare = (flare: SunFlare) => {
      const pulse = Math.sin(time * flare.pulseSpeed + flare.pulseOffset) * 0.3 + 0.7
      const opacity = flare.opacity * pulse

      ctx.save()
      ctx.globalAlpha = opacity
      
      // Create sun flare gradient
      const gradient = ctx.createRadialGradient(flare.x, flare.y, 0, flare.x, flare.y, flare.size)
      gradient.addColorStop(0, flare.color + '30')
      gradient.addColorStop(0.2, flare.color + '20')
      gradient.addColorStop(0.5, flare.color + '10')
      gradient.addColorStop(1, 'transparent')
      
      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.arc(flare.x, flare.y, flare.size, 0, Math.PI * 2)
      ctx.fill()
      
      ctx.restore()
    }

    // Draw shooting star
    const drawShootingStar = (star: ShootingStar) => {
      const opacity = star.opacity * (star.life / star.maxLife)

      ctx.save()
      ctx.globalAlpha = opacity
      ctx.strokeStyle = star.color
      ctx.lineWidth = 1.5
      ctx.lineCap = 'round'

      // Create gradient tail
      const gradient = ctx.createLinearGradient(
        star.x, star.y,
        star.x - star.vx * star.length / 4, star.y - star.vy * star.length / 4
      )
      gradient.addColorStop(0, star.color)
      gradient.addColorStop(1, 'transparent')

      ctx.strokeStyle = gradient
      ctx.beginPath()
      ctx.moveTo(star.x, star.y)
      ctx.lineTo(
        star.x - star.vx * star.length / 4,
        star.y - star.vy * star.length / 4
      )
      ctx.stroke()

      // Bright head
      ctx.fillStyle = star.color
      ctx.beginPath()
      ctx.arc(star.x, star.y, 1.5, 0, Math.PI * 2)
      ctx.fill()

      ctx.restore()
    }

    // Animation loop
    const animate = (currentTime: number) => {
      time = currentTime * 0.001

      // Create shooting stars occasionally
      if (currentTime - lastShootingStarTime > 4000 + Math.random() * 6000) {
        shootingStars.push(createShootingStar())
        lastShootingStarTime = currentTime
      }

      // Clear canvas with deep space background
      ctx.fillStyle = '#000008'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Add subtle gradient overlay for depth
      const gradient = ctx.createRadialGradient(
        canvas.width * 0.3, canvas.height * 0.4, 0,
        canvas.width * 0.3, canvas.height * 0.4, Math.max(canvas.width, canvas.height) * 0.8
      )
      gradient.addColorStop(0, 'rgba(10, 10, 25, 0.2)')
      gradient.addColorStop(0.5, 'rgba(5, 5, 15, 0.4)')
      gradient.addColorStop(1, 'rgba(0, 0, 8, 0.8)')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw sun flares first (background layer)
      sunFlares.forEach(flare => {
        drawSunFlare(flare)
      })

      // Update and draw galaxy particles
      galaxyParticles.forEach(particle => {
        particle.angle += particle.speed
        particle.x = (canvas.width * 0.3) + Math.cos(particle.angle) * particle.distance
        particle.y = (canvas.height * 0.4) + Math.sin(particle.angle) * particle.distance * 0.6

        drawGalaxyParticle(particle)
      })

      // Update and draw shooting stars
      shootingStars = shootingStars.filter(star => {
        star.x += star.vx
        star.y += star.vy
        star.life--
        star.opacity = star.life / star.maxLife

        if (star.life <= 0 ||
            star.x < -100 || star.x > canvas.width + 100 ||
            star.y < -100 || star.y > canvas.height + 100) {
          return false
        }

        drawShootingStar(star)
        return true
      })

      // Draw stars (foreground layer)
      stars.forEach(star => {
        // Very subtle movement for parallax
        star.x += Math.sin(time * 0.05 + star.twinkleOffset) * 0.05
        star.y += Math.cos(time * 0.05 + star.twinkleOffset) * 0.05

        drawStar(star)
      })

      animationFrameId = requestAnimationFrame(animate)
    }

    // Initialize
    resizeCanvas()
    animate(0)

    // Event listeners
    window.addEventListener("resize", resizeCanvas)
    window.addEventListener("mousemove", handleMouseMove)

    // Cleanup
    return () => {
      window.removeEventListener("resize", resizeCanvas)
      window.removeEventListener("mousemove", handleMouseMove)
      cancelAnimationFrame(animationFrameId)
    }
  }, [section])

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 z-0 pointer-events-none transition-opacity duration-1000 ${
        isLoaded ? 'opacity-100' : 'opacity-0'
      }`}
      aria-hidden="true"
    />
  )
}
