"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowD<PERSON>, Github, Linkedin, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

export function HeroSection() {
  const [typedText, setTypedText] = useState("")
  const fullText = "Full-Stack Developer | UI/UX Enthusiast"
  const [showCursor, setShowCursor] = useState(true)

  useEffect(() => {
    if (typedText.length < fullText.length) {
      const timeout = setTimeout(() => {
        setTypedText(fullText.slice(0, typedText.length + 1))
      }, 50)
      return () => clearTimeout(timeout)
    }
  }, [typedText, fullText])

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor((prev) => !prev)
    }, 500)
    return () => clearInterval(cursorInterval)
  }, [])

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative pt-16 overflow-hidden">
      {/* Cosmic floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-gradient-to-br from-primary/20 to-blue-500/20 backdrop-blur-3xl floating opacity-30 transform-gpu perspective-1000 rotate-12 star-twinkle"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-3xl floating-delay-1 opacity-30 transform-gpu perspective-1000 -rotate-12 cosmic-drift"></div>
        <div className="absolute bottom-1/3 left-1/3 w-28 h-28 rounded-full bg-gradient-to-br from-cyan-500/20 to-blue-500/20 backdrop-blur-3xl floating-delay-2 opacity-30 transform-gpu perspective-1000 rotate-45 star-twinkle"></div>
        <div className="absolute bottom-1/4 right-1/3 w-20 h-20 rounded-lg bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-3xl floating-delay-3 opacity-30 transform-gpu perspective-1000 -rotate-45 cosmic-drift"></div>
      </div>

      <div className="container mx-auto px-4 py-16 flex flex-col items-center text-center relative z-10">
        <div className="max-w-4xl transform-gpu">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 cosmic-text transform hover:scale-105 transition-transform duration-300">
            Hi, I'm <span className="text-primary bg-clip-text text-transparent bg-gradient-to-r from-primary via-blue-400 to-purple-400">Zaid Ahmed S</span>
          </h1>
          <h2 className="text-xl md:text-2xl font-medium mb-6 text-white/80 transform hover:scale-102 transition-transform duration-300">
            {typedText}
            <span className={cn("ml-1 inline-block w-2 h-8 bg-primary", showCursor ? "opacity-100" : "opacity-0")}>
              &nbsp;
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto mb-8 text-lg transform hover:scale-102 transition-transform duration-300">
            Building interactive, scalable, and user-friendly web experiences.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 mb-12 justify-center">
            <Button
              size="lg"
              className="bg-primary/20 border border-primary/50 hover:bg-primary/30 text-primary galaxy-glow transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-lg hover:shadow-primary/25"
              onClick={() => document.getElementById("projects").scrollIntoView({ behavior: "smooth" })}
            >
              View My Work
            </Button>

            <Button
              size="lg"
              variant="outline"
              className="nebula-card border-white/20 hover:border-white/40 transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-lg hover:shadow-white/25"
              onClick={() => window.location.href = "mailto:<EMAIL>"}
            >
              Contact Me
            </Button>
          </div>

          <div className="flex space-x-6 mb-16 justify-center">
            <a
              href="https://github.com/Zaidgit26"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-all duration-300 galaxy-glow p-3 rounded-full nebula-card hover:border-primary/30 transform hover:scale-110 hover:-translate-y-1 hover:rotate-3 star-twinkle"
            >
              <Github size={24} />
              <span className="sr-only">GitHub</span>
            </a>
            <a
              href="https://in.linkedin.com/in/zaid-ahmed-s-33008023b"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-all duration-300 galaxy-glow p-3 rounded-full nebula-card hover:border-primary/30 transform hover:scale-110 hover:-translate-y-1 hover:-rotate-3 star-twinkle"
            >
              <Linkedin size={24} />
              <span className="sr-only">LinkedIn</span>
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-all duration-300 galaxy-glow p-3 rounded-full nebula-card hover:border-primary/30 transform hover:scale-110 hover:-translate-y-1 hover:rotate-3 star-twinkle"
            >
              <ExternalLink size={24} />
              <span className="sr-only">Portfolio</span>
            </a>
          </div>
        </div>

        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
          <div className="text-white/50 mb-2 text-sm animate-pulse">Scroll Down</div>
          <a
            href="#about"
            className="animate-bounce nebula-card galaxy-glow rounded-full p-4 border border-primary/30 hover:border-primary/50 transition-all duration-300 transform hover:scale-110 hover:shadow-lg hover:shadow-primary/25 star-twinkle"
            aria-label="Scroll down"
          >
            <ArrowDown size={20} className="text-primary" />
          </a>
        </div>

        {/* Cosmic nebula elements */}
        <div className="absolute top-1/4 left-10 w-32 h-32 rounded-full bg-gradient-to-br from-primary/15 to-blue-500/15 backdrop-blur-3xl floating opacity-30 hidden lg:block star-twinkle"></div>
        <div className="absolute bottom-1/4 right-10 w-40 h-40 rounded-full bg-gradient-to-br from-blue-500/15 to-purple-500/15 backdrop-blur-3xl floating-delay-1 opacity-30 hidden lg:block cosmic-drift"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-gradient-to-br from-purple-500/15 to-cyan-500/15 backdrop-blur-3xl floating-delay-2 opacity-30 hidden lg:block star-twinkle"></div>
        <div className="absolute bottom-1/3 left-1/4 w-36 h-36 rounded-full bg-gradient-to-br from-cyan-500/15 to-primary/15 backdrop-blur-3xl floating-delay-3 opacity-30 hidden lg:block cosmic-drift"></div>
      </div>
    </section>
  )
}